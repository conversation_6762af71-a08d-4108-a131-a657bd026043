-- MySQL Database Setup for AI Code Companion
-- Run this script in your MySQL server to create the database

CREATE DATABASE IF NOT EXISTS aicompanion;
USE aicompanion;

-- The tables will be created automatically by <PERSON><PERSON><PERSON> when you run `npm run db:push`
-- This script just ensures the database exists

-- Optional: Create a dedicated user for the application
-- CREATE USER 'aicompanion'@'localhost' IDENTIFIED BY 'your_password_here';
-- GRANT ALL PRIVILEGES ON aicompanion.* TO 'aicompanion'@'localhost';
-- FLUSH PRIVILEGES;
