-- MySQL Database Setup for Employee Leave Management System
-- Run this script in your MySQL server to create the database

CREATE DATABASE IF NOT EXISTS employee_leave_management_system;
USE employee_leave_management_system;

-- The tables will be created automatically by <PERSON><PERSON><PERSON> when you run `npm run db:push`
-- This script just ensures the database exists

-- Optional: Create a dedicated user for the application
-- CREATE USER 'employee_leave_mgmt'@'localhost' IDENTIFIED BY 'your_password_here';
-- GRANT ALL PRIVILEGES ON employee_leave_management_system.* TO 'employee_leave_mgmt'@'localhost';
-- FLUSH PRIVILEGES;
