# MySQL Setup Instructions

This application has been configured to use MySQL instead of PostgreSQL. Follow these steps to set up your local MySQL database:

## Prerequisites

1. **Install MySQL Server** on your local machine if you haven't already:
   - **Windows**: Download from [MySQL official website](https://dev.mysql.com/downloads/mysql/)
   - **macOS**: Use Homebrew: `brew install mysql`
   - **Linux**: Use your package manager: `sudo apt-get install mysql-server` (Ubuntu/Debian)

2. **Start MySQL Service**:
   - **Windows**: MySQL should start automatically, or use MySQL Workbench
   - **macOS**: `brew services start mysql`
   - **Linux**: `sudo systemctl start mysql`

## Database Setup

1. **Create the database**:
   ```bash
   mysql -u root -p < setup-mysql.sql
   ```
   
   Or manually in MySQL command line:
   ```sql
   CREATE DATABASE IF NOT EXISTS employee_leave_management_system;
   ```

2. **Configure environment variables**:
   The `.env` file has been created with default MySQL settings:
   ```
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=root
   DB_PASSWORD=
   DB_NAME=employee_leave_management_system
   ```
   
   **Update the `.env` file** with your MySQL credentials:
   - Set `DB_PASSWORD` to your MySQL root password
   - Optionally change `DB_USER` if you created a dedicated user

3. **Push the database schema**:
   ```bash
   npm run db:push
   ```
   
   This will create the necessary tables (`users` and `leaves`) in your MySQL database.

## Running the Application

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **The application will be available at**: `http://localhost:5000`

## Troubleshooting

- **Connection refused**: Make sure MySQL service is running
- **Access denied**: Check your username and password in `.env`
- **Database doesn't exist**: Run the setup SQL script first
- **Port conflicts**: Make sure port 3306 is available or change `DB_PORT` in `.env`

## What Changed from PostgreSQL

- Database driver changed from `@neondatabase/serverless` to `mysql2`
- Schema updated to use MySQL-specific syntax (`mysqlTable`, `int` instead of `serial`)
- Connection configuration changed to use individual credentials instead of connection URL
- Removed PostgreSQL-specific dependencies and types
