Here’s a **well-optimized, agentic prompt** you can give to your AI coding assistant (inside VS Code or a Copilot-like tool) to build a **clean, scalable, and submission-ready prototype** for your Employee Leave Management System assessment:

---

### ✅ **Prompt to AI Coding Agent (Full-stack Leave Management App):**

> You are my full-stack AI developer. I need you to build a complete and responsive **Employee Leave Management System** as part of a technical assessment.
>
> 🔧 **Tech Stack:**
>
> * **Frontend:** ReactJS with Material-UI (MUI v5), Formik, and Yup
> * **Backend:** NodeJS with ExpressJS
> * **Database:** MySQL (use Sequelize ORM or raw queries)
>
> 🎯 **Core Features Required:**
>
> 1. **Leave Application Form (Formik + Yup):**
>
>    * Fields: Employee Name, Leave Type (dropdown), From Date, To Date, Reason (textarea)
>    * Use Yup for form validation
>    * Submit button should be disabled during validation/loading
> 2. **Leave Records Table (MUI Table):**
>
>    * Shows all submitted leave records with the following columns:
>
>      * Employee Name, Leave Type, Date Range, Reason, Status
>    * Fetch from backend using Axios
> 3. **Admin Panel (Optional Enhancement):**
>
>    * Ability to approve/reject leave from the UI
>    * Status should update accordingly in the database
> 4. **Backend API Routes:**
>
>    * `POST /apply-leave` → Store new leave request
>    * `GET /leaves` → Fetch all leave records
>    * `PUT /leaves/:id/approve` → Approve a leave
>    * `PUT /leaves/:id/reject` → Reject a leave
> 5. **MySQL Table Design:**
>
> ```sql
> CREATE TABLE leaves (
>   id INT AUTO_INCREMENT PRIMARY KEY,
>   employee_name VARCHAR(100),
>   leave_type VARCHAR(50),
>   from_date DATE,
>   to_date DATE,
>   reason TEXT,
>   status ENUM('Pending', 'Approved', 'Rejected') DEFAULT 'Pending'
> );
> ```
>
> 🖼️ **UI Design:**
>
> * Use MUI Grid and Cards for layout
> * Must be mobile-responsive and clean
> * Use MUI DatePicker for date inputs
>
> 🔁 **Data Flow:**
>
> * On form submit → send POST request to backend
> * On page load → fetch all leave records to show in a table
> * On status update → PUT to backend and re-render table
>
> 📂 **Folder Structure Hint:**
>
> ```
> /frontend/src/components/
> /frontend/src/pages/
> /backend/routes/
> /backend/controllers/
> ```
>
> ✅ **Bonus Enhancements (if time allows):**
>
> * Add Login for Employee/Admin roles
> * Filter leaves by status (Pending, Approved, Rejected)
> * Confirmation dialog before status change
>
> Generate **clean, modular, readable code** with comments. Follow **React functional component standards** and use **Express middleware** properly. Output all required backend and frontend files to run the system locally via `npm start` and `uvicorn main:app --reload`.

---

Let me know if you want me to prepare the GitHub README.md, sample DB, or auto-deployment instructions!
